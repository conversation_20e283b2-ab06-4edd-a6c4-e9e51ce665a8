<?php

/**
 * Filter
 */

$page_obj = get_queried_object();

$_query = get_query_var('data');

$types = get_terms(array( 'taxonomy' => 'holiday-type', 'parent' => 0 ));
$regions = get_terms('holiday-regions');

if (is_tax()) {
    $regions = get_term_children($page_obj->term_id, 'holiday-regions');

    if (is_tax('holiday-type')) {
        $regions = get_terms(
            [
                'taxonomy' => 'holiday-regions',
                'parent'    => 0
            ]);
    }
}

$_taxonomies = get_object_taxonomies('holiday');

$found_terms = [];


if (!empty($_query->posts)) {
    foreach ($_query->posts as $_post) {

        if ($_taxonomies) {
            foreach ($_taxonomies as $_taxonomy) {
                $post_terms = get_the_terms($_post, $_taxonomy);
                if($post_terms) {
                    foreach ($post_terms as $post_term) {
                        $found_terms[] = $post_term;
                    }
                }

            }
        }
    }
}


$found_terms = super_unique($found_terms);

?>


<div class="filter">
    <div class="filter__inner">
        <div class="filter__heading">
            <span><?php _e('Refine results', 'absoluteescapes'); ?></span>
        </div>
        <form id="filterForm" class="filter__form" action="#holidaysResults" method="get">
<!--            --><?php //$term = get_queried_object();
//                if ($term->parent === 3 && $term->taxonomy === 'holiday-type'): $child_of_drive = true; endif ?>
<!--            --><?php
//                if(is_tax('holiday-type', 'self-drive-holidays') || is_tax('holiday-type', 'walking-holidays') || $child_of_drive === true): ?>
<!--                -->
<!--                <div class="filter__dropdowns">-->
<!--                    -->
<!--                    <div class="filter__dropdown">-->
<!--                        <div class="filter__label-wrapper">-->
<!--                            <span class="filter__label">--><?php //_e('Duration', 'absoluteescapes'); ?><!--</span>-->
<!--                            <i class="fas fa-chevron-up"></i>-->
<!--                        </div>-->
<!--                        <div class="filter__inputs">-->
<!--                            <div class="filter__range">-->
<!--                                <span class="filter__range-number filter__range-number--min">--><?php //echo ($_GET['durationmin']) ? $_GET['durationmin'] : '1'; ?><!--</span>-->
<!--                                --->
<!--                                <span class="filter__range-number filter__range-number--max">--><?php //echo ($_GET['durationmax']) ? $_GET['durationmax'] : '32'; ?><!--</span>-->
<!--                                <span class="filter__range-post-text">--><?php //_e('days', 'absoluteescapes'); ?><!--</span>-->
<!--                            </div>-->
<!---->
<!--                            <div class="filter__input-wrapper filter__input-wrapper--range">-->
<!--                                <div id="distanceRangeSlider"></div>-->
<!--                                <input type="hidden" name="durationmin" id="durationMin"-->
<!--                                       value="--><?php //echo $_GET['durationmin']; ?><!--">-->
<!--                                <input type="hidden" name="durationmax" id="durationMax"-->
<!--                                       value="--><?php //echo $_GET['durationmax']; ?><!--">-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            --><?php //else: ?>
                <div class="filter__dropdowns">
                    <?php if ($types && !is_tax('holiday-type')) : ?>
                        <div class="filter__dropdown">
                            <div class="filter__label-wrapper">
                                <span class="filter__label"><?php _e('Type of holiday', 'absoluteescapes'); ?></span>
                                <i class="fas fa-chevron-up"></i>
                            </div>
                            <div class="filter__inputs">
                                <?php foreach ($types as $type) : ?>

                                    <?php if(!in_array($type, $found_terms)) {
                                        continue;
                                    } ?>
                                    <div class="filter__input-wrapper">
                                        <input id="inputType-<?php echo $type->slug; ?>" type="checkbox"
                                               class="filter__input" name="type[]" value="<?php echo $type->slug; ?>"
                                               <?php if (isset($_GET['type'])) : if (in_array($type->slug, $_GET['type'])) : ?>checked<?php endif;
                                        endif; ?>>
                                        <label for="inputType-<?php echo $type->slug; ?>"><?php echo $type->name; ?></label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php if ($regions) : ?>
                        <div class="filter__dropdown">
                            <div class="filter__label-wrapper">
                                <span class="filter__label"><?php _e('Region', 'absoluteescapes'); ?></span>
                                <i class="fas fa-chevron-up"></i>
                            </div>
                            <div class="filter__inputs">
                                <?php foreach ($regions as $region) : ?>
                                    <?php $rterm = get_term($region); ?>

                                    <?php if(!in_array($rterm, $found_terms)) {
                                        continue;
                                    } ?>
                                    <div class="filter__input-wrapper">

                                        <input id="inputType-<?php echo $rterm->slug; ?>" type="checkbox"
                                               class="filter__input" name="region[]" value="<?php echo $rterm->slug; ?>"
                                               <?php if (isset($_GET['region'])) : if (in_array($rterm->slug, $_GET['region'])) : ?>checked<?php endif;
                                        endif; ?>>
                                        <label for="inputType-<?php echo $rterm->slug; ?>"><?php echo $rterm->name; ?></label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    <div class="filter__dropdown">
                        <div class="filter__label-wrapper">
                            <span class="filter__label"><?php _e('Duration', 'absoluteescapes'); ?></span>
                            <i class="fas fa-chevron-up"></i>
                        </div>
                        <div class="filter__inputs">
                            <div class="filter__range">
                                <span class="filter__range-number filter__range-number--min"><?php echo ($_GET['durationmin']) ? $_GET['durationmin'] : '1'; ?></span>
                                -
                                <span class="filter__range-number filter__range-number--max"><?php echo ($_GET['durationmax']) ? $_GET['durationmax'] : '32'; ?></span>
                                <span class="filter__range-post-text"><?php _e('nights', 'absoluteescapes'); ?></span>
                            </div>

                            <div class="filter__input-wrapper filter__input-wrapper--range">
                                <div id="distanceRangeSlider"></div>
                                <input type="hidden" name="durationmin" id="durationMin"
                                       value="<?php echo $_GET['durationmin']; ?>">
                                <input type="hidden" name="durationmax" id="durationMax"
                                       value="<?php echo $_GET['durationmax']; ?>">
                            </div>
                        </div>
                    </div>
                </div>
<!--            --><?php //endif; ?>
            <input type="hidden" name="sort" id="sort"
                   value="<?php echo ($_GET['sort']) ? $_GET['sort'] : 'most-popular'; ?>">
        </form>

    </div>
</div>
